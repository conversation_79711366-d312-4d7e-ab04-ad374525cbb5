"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useAppointment } from "./AppointmentContext"
import { actions } from 'astro:actions'
import toast from "react-hot-toast"

export default function PlatesAndKmForm({ setError }: { setError: (error: string | null) => void }) {
  const {
    plates,
    setPlates,
    km,
    setKm,
    setAppointmentInfo,
    setCurrentStep,
    setLoading,
    loading
  } = useAppointment()

  const [platesError, setPlatesError] = useState("")
  const [kmError, setKmError] = useState("")

  const validateForm = () => {
    let isValid = true

    if (!plates.trim()) {
      setPlatesError("Las placas son requeridas")
      isValid = false
    } else {
      setPlatesError("")
    }

    if (!km.trim()) {
      setKmError("El kilometraje es requerido")
      isValid = false
    } else if (isNaN(Number(km)) || Number(km) <= 0) {
      setKmError("El kilometraje debe ser un número positivo")
      isValid = false
    } else {
      setKmError("")
    }

    return isValid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!validateForm()) return

    try {
      setLoading(true)

      const result = await actions.getAppointmentInfo({
        plates: plates.trim(),
        km: parseInt(km)
      })
      console.log('result for [getAppointmentInfo]', result);
      if (!result.data?.success || !result.data) {
        toast.error(result.data?.error?.message || "Error al obtener información de la cita")
        setError(result.data?.error?.message || "Error al obtener información de la cita")
        return
      }

      // Verificar si ya existe una cita agendada
      if (result.data.data.appointment) {
        // Guardar la información en el contexto
        setAppointmentInfo(result.data.data)

        // Avanzar al paso de cita existente (vamos a crear este paso)
        setCurrentStep(7) // Nuevo paso para mostrar cita existente
      } else {
        // Guardar la información en el contexto
        setAppointmentInfo(result.data.data)

        // Avanzar al siguiente paso normal
        setCurrentStep(2)
      }
    } catch (error: any) {
      toast.error(error.message || "Error al obtener información de la cita")
      setError(error.message || "Error al obtener información de la cita")
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="plates" className="block text-sm font-medium text-gray-700 mb-1">
          Placas del vehículo
        </label>
        <Input
          id="plates"
          value={plates}
          onChange={(e) => setPlates(e.target.value.toUpperCase())}
          placeholder="Ej. ABC123"
          className="w-full"
        />
        {platesError && <p className="mt-1 text-sm text-red-600">{platesError}</p>}
      </div>

      <div>
        <label htmlFor="km" className="block text-sm font-medium text-gray-700 mb-1">
          Kilometraje actual
        </label>
        <Input
          id="km"
          type="number"
          value={km}
          onChange={(e) => setKm(e.target.value)}
          placeholder="Ej. 45000"
          className="w-full"
        />
        {kmError && <p className="mt-1 text-sm text-red-600">{kmError}</p>}
      </div>

      <p className="text-sm text-gray-600">
        El kilometraje ingresado debe ser una estimación lo más precisa posible del que marca el vehículo al momento del registro. En caso de una discrepancia significativa, el conductor asume la responsabilidad y se expone a posibles cargos por agendamiento incorrecto del servicio.
      </p>


      <Button
        type="submit"
        className="w-full bg-violet-700 hover:bg-violet-800 text-white"
        disabled={loading}
      >
        {loading ? "Verificando..." : "Continuar"}
      </Button>
    </form>
  )
}
