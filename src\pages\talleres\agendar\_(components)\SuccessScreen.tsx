"use client"

import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle2 } from "lucide-react"
import { useAppointment } from "./AppointmentContext"

export default function SuccessScreen() {
  const { setCurrentStep, setPlates, setKm, setAppointmentInfo, setSelectedWorkshop, 
    setSelectedServiceType, setSelectedDate, setAvailableSlots, setSelectedSlot } = useAppointment()

  const handleStartOver = () => {
    // Reiniciar todos los estados
    setPlates('')
    setKm('')
    setAppointmentInfo(null)
    setSelectedWorkshop(null)
    setSelectedServiceType(null)
    setSelectedDate(null)
    setAvailableSlots([])
    setSelectedSlot(null)
    setCurrentStep(1)
  }

  return (
    <div className="text-center space-y-4 py-6">
      <div className="flex justify-center">
        <CheckCircle2 className="h-16 w-16 text-green-500" />
      </div>
      
      <h2 className="text-2xl font-bold">¡Cita agendada con éxito!</h2>
      
      <p className="text-gray-600">
        Tu cita de mantenimiento ha sido agendada correctamente. 
        Recibirás un correo electrónico con los detalles de tu cita.
      </p>
      
      <div className="pt-4">
        <Button 
          className="bg-violet-700 hover:bg-violet-800 text-white"
          onClick={handleStartOver}
        >
          Volver al inicio
        </Button>
      </div>
    </div>
  )
}