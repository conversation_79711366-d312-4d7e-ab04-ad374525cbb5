---
import Layout from '@/layouts/Layout.astro';
import Steps from './_(components)/Steps';
import {appointmentService} from '@/actions/appointmentService';

const { appointmentId } = Astro.params as { appointmentId: string };

const result = await appointmentService.getAppointmentById(appointmentId);
console.log('result', result);
---

<Layout title='Reagendar cita'>
    {
      result.success ? (
        <Steps client:load data={result.data} />
      ) : (
      <div class="fixed inset-0 flex items-center justify-center bg-gray-200">
        <div class="bg-white p-4 rounded-lg shadow-lg space-y-4 w-[300px]">
          <h2 class="text-xl font-bold text-center"> No se encontró ninguna cita</h2>
        </div>
      </div>

      )
    }
</Layout>