0000000000000000000000000000000000000000 0215f440ac42a3a774a08f7db48854e638163e1a <PERSON> <<EMAIL>> 1739482183 -0600	commit (initial): Initial commit from Astro
0215f440ac42a3a774a08f7db48854e638163e1a c3dac84d1988d6ce69832c6cea3dd3152621fd4f <PERSON> <<EMAIL>> 1739483582 -0600	commit: first commit, project initialization and configurations
c3dac84d1988d6ce69832c6cea3dd3152621fd4f 3bb56a5643ed939835371e29d2c5e59c3c65c797 <PERSON> <<EMAIL>> 1739485041 -0600	commit: server configuration
3bb56a5643ed939835371e29d2c5e59c3c65c797 21e589726aad6df68ed671b823f183eb319fb426 <PERSON> <<EMAIL>> 1739567834 -0600	commit: saving progress
21e589726aad6df68ed671b823f183eb319fb426 a7d9809b78724f82ab7725654c44fa6e3f1a2bad Pedro Martinez <<EMAIL>> 1739568587 -0600	commit: add axios to package.json and URL API env
a7d9809b78724f82ab7725654c44fa6e3f1a2bad b15157118b516d986668b34027708fe8cc8aa2cd Pedro Martinez <<EMAIL>> 1739573011 -0600	commit: downgrade lucide-react version due to vercel error
b15157118b516d986668b34027708fe8cc8aa2cd ed58dfd0d7d7cfe977288c2b53df2166d481ff43 Pedro Martinez <<EMAIL>> 1739573161 -0600	commit: .
ed58dfd0d7d7cfe977288c2b53df2166d481ff43 81406f40dcc6b30ca3bae7cd1d61ffa73984daf9 Pedro Martinez <<EMAIL>> 1739573258 -0600	commit: .
81406f40dcc6b30ca3bae7cd1d61ffa73984daf9 7999aefd751166289bc39a9d323a813c58917fec Pedro Martinez <<EMAIL>> 1739910321 -0600	commit: change env name
7999aefd751166289bc39a9d323a813c58917fec cc00ea90965e5dd3d56b66e262bdec2ef3c23225 Pedro Martinez <<EMAIL>> 1739910714 -0600	commit: change get available slots to an action to avoid revealing the backend url
cc00ea90965e5dd3d56b66e262bdec2ef3c23225 cc00ea90965e5dd3d56b66e262bdec2ef3c23225 Pedro Martinez <<EMAIL>> 1744233119 -0600	checkout: moving from master to complete-company-service
cc00ea90965e5dd3d56b66e262bdec2ef3c23225 9466d4bdb90f5ecfcd79408e4f45e923e09481a5 Pedro Martinez <<EMAIL>> 1744233150 -0600	commit: Complete book appointment done
