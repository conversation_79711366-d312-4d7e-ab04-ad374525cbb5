interface TimeRange {
  start: string; // Format "HH:mm"
  end: string;   // Format "HH:mm"
}

interface WeeklySchedule {
  monday?: TimeRange;
  tuesday?: TimeRange;
  wednesday?: TimeRange;
  thursday?: TimeRange;
  friday?: TimeRange;
  saturday?: TimeRange;
  sunday?: TimeRange;
  [key: string]: TimeRange | undefined;
}

interface WorkshopCapacity {
  totalBays: number;
  techniciansPerBay: number;
}

interface ScheduleConfig {
  weeklySchedule: WeeklySchedule;
  appointmentDuration: number;
  maxSimultaneousAppointments: number;
  timezone: string;
  breakTime?: TimeRange;
  bufferTime?: number;
  capacity: WorkshopCapacity;
}

interface ModifiedSchedule {
  workingHours?: TimeRange
  breakTime?: TimeRange
  capacity?: WorkshopCapacity
}

type OverrideType = "BLOCKED" | "MODIFIED"
type OverrideScope = "SINGLE" | "YEARLY"

interface ScheduleOverrideFormData {
  name: string
  description?: string
  isDateRange: boolean
  startDate: Date | null
  endDate: Date | null
  type: OverrideType
  scope: OverrideScope
  modifiedSchedule?: ModifiedSchedule
  isWorkshop?: boolean
}

interface CustomerData {
  name: string;
  email: string;
  phone: string;
}

interface DayInfo {
  date: Date | null;
  disabled: boolean;
  isToday?: boolean;
}

interface ServiceType {
  _id: string;
  name: string;
  description?: string;
  duration: number;
  organization: string;
  isActive: boolean;
}
