"use client"

import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Clock, LoaderCircle } from "lucide-react"
import { useAppointment } from "./AppointmentContext"
import { DateTime } from "luxon"

export default function TimeSlotSelector({ setError }: { setError: (error: string | null) => void }) {
  const { 
    availableSlots, 
    selectedSlot, 
    setSelectedSlot, 
    setCurrentStep, 
    selectedDate,
    loading,
    selectedServiceType
  } = useAppointment()

  // Reiniciar el error al montar el componente
  useEffect(() => {
    setError(null)
  }, [setError])

  const goBack = () => {
    setCurrentStep(3)
  }

  const formatTimeRange = (startTime: string) => {
    const start = DateTime.fromISO(startTime);
    const end = start.plus({ minutes: selectedServiceType?.duration || 60 });

    const startFormatted = start.toFormat('hh:mm a').toLowerCase();
    const endFormatted = end.toFormat('hh:mm a').toLowerCase();

    return `${startFormatted} - ${endFormatted}`;
  };

  return (
    <div className="p-2 space-y-4">
      <Button variant="ghost" onClick={goBack}>
        <ArrowLeft className="mr-2" /> Regresar
      </Button>
      
      <h2 className="text-xl font-bold text-center">Selecciona un horario</h2>
      
      {loading ? (
        <div className="flex flex-col items-center justify-center py-10">
          <LoaderCircle className="h-10 w-10 text-violet-700 animate-spin mb-4" />
          <p className="text-gray-600">Cargando horarios disponibles...</p>
        </div>
      ) : (
        <>
          {availableSlots.length === 0 ? (
            <div className="text-center p-4">
              <p>No hay horarios disponibles para la fecha seleccionada.</p>
              <Button 
                  variant="outline"
                  onClick={() => setCurrentStep(3)}
                  className="mt-4"
                >
                  Seleccionar otra fecha
                </Button>
            </div>
            ) : (
              <div className="flex flex-col gap-3 mx-auto items-center">
                {availableSlots.map((slot) => (
                  <Button
                    key={slot}
                    variant="outline"
                    onClick={() => {
                      setSelectedSlot(slot);
                      setCurrentStep(5);
                    }}
                    className="p-4 px-10"
                  >
                    <div className="flex items-center gap-2 ">
                      <Clock className="w-4 h-4" />
                      <span>{formatTimeRange(slot)}</span>
                    </div>
                    {/*  {
                      selectedServiceType && (
                        <span className="text-sm text-gray-500">
                          Duración: {selectedServiceType.duration} minutos
                        </span>
                      )
                    } */}
                  </Button>
                ))}
            </div>
          )}
        </>
      )}
    </div>
  )
}

