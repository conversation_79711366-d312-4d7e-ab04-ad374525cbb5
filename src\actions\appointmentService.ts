import { URL_API } from '@/constants';
import axios from 'axios';

export const apiVendorPlatform = axios.create({
  baseURL: URL_API + '/vendor-platform',
  headers: {
    'Content-Type': 'application/json',
  }
});

export const appointmentService = {


  getAvailableSlots: async (workshopId: string, date: string, serviceTypeId: string) => {
    try {
      const { data } = await apiVendorPlatform.get(`/public/workshops/${workshopId}/available-slots/${date}/${serviceTypeId}`);
      console.log('data', data);
      return {
        success: true,
        data: data.data,
        error: null
      }

    } catch (error) {
      console.log('error', error);
      return {
        success: false,
        data: null,
        error
      }
    }
  },

  getAvailableSlotsOld: async (workshopId: string, date: string, serviceTypeId: string) => {
    const { data } = await apiVendorPlatform.get(`/public/workshops/${workshopId}/available-slots/${date}/${serviceTypeId}`);
    return data;
  },

  getAppointmentById: async (appointmentId: string) => {
    try {
      const { data } = await apiVendorPlatform.get(`/appointments/${appointmentId}?pAssociate=true&pStockId=true&pServiceType=true`);
      return {
        success: true,
        data: data.data,
        error: null
      }

    } catch (error) {
      return {
        success: false,
        data: null,
        error
      }
    }
  },


  reescheduleAppointment: async (appointmentId: string, startTime: string, serviceTypeId: string) => {
    try {
      const { data } = await apiVendorPlatform.put(`/public/appointments/${appointmentId}/reschedule`, { startTime, serviceTypeId });
      return data;

    } catch (error) {
      console.log('[reescheduleAppointment] error', error);
      throw new Error('Error al reagendar la cita');
    }
  },


  cancelAppointment: async (appointmentId: string) => {
    const { data } = await apiVendorPlatform.delete(`/appointments/${appointmentId}/cancel`);
    return data;
  },

  notAttendedAppointment: async (appointmentId: string) => {
    const { data } = await apiVendorPlatform.patch(`/appointments/${appointmentId}/not-attended`);
    return data;
  }

};