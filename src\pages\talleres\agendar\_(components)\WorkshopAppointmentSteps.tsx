"use client"

import { useState } from "react"
import { AppointmentProvider } from "./AppointmentContext"
import PlatesAndKmForm from "./PlatesAndKmForm"
import WorkshopSelector from "./WorkshopSelector"
import AppointmentCalendar from "./AppointmentCalendar"
import TimeSlotSelector from "./TimeSlotSelector"
import AppointmentSummary from "./AppointmentSummary"
import SuccessScreen from "./SuccessScreen"
import ExistingAppointment from "./ExistingAppointment"
import { useAppointment } from "./AppointmentContext"


export default function WorkshopAppointmentSteps() {
  return (
    <AppointmentProvider>
      <StepsContainer />
    </AppointmentProvider>
  )
}

function StepsContainer() {
  const [error, setError] = useState<string | null>(null)

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4  md:p-6">
        <h1 className="text-2xl font-bold text-center mb-6">Agendar Cita de Mantenimiento</h1>
        <StepRenderer setError={setError} />
        {error && (
          <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
            {error.split("\n").map((line, index) => (
              <span key={index}>{line}<br /></span>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

function StepRenderer({ setError }: { setError: (error: string | null) => void }) {
  const { currentStep } = useAppointment()

  switch (currentStep) {
    case 1:
      return <PlatesAndKmForm setError={setError} />
    case 2:
      return <WorkshopSelector setError={setError} />
    case 3:
      return <AppointmentCalendar setError={setError} />
    case 4:
      return <TimeSlotSelector setError={setError} />
    case 5:
      return <AppointmentSummary setError={setError} />
    case 6:
      return <SuccessScreen />
    case 7:
      return <ExistingAppointment setError={setError} />
    default:
      return <PlatesAndKmForm setError={setError} />
  }
}

