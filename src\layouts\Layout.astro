---
import '../styles/global.css';

interface Props {
  title: string;
}
import { Toaster } from 'react-hot-toast';

const {title} = Astro.props;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<meta name="robots" content="noindex">
    <meta name="googlebot" content="noindex">
    <meta name="bingbot" content="noindex">
    <meta name="yandex" content="noindex">
		<title>{title}</title>
	</head>
	<body>
		<Toaster client:load />
		<slot />
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		height: 100%;
	}
</style>
