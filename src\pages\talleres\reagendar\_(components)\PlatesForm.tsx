"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { LoaderCircle, Calendar } from "lucide-react"
import { actions } from "astro:actions"
import toast from "react-hot-toast"
import { useReschedule } from "./RescheduleContext"

export default function PlatesForm() {
  const {
    plates,
    setPlates,
    setAppointmentInfo,
    setCurrentStep,
    loading,
    setLoading,
    setError
  } = useReschedule()

  const [localPlates, setLocalPlates] = useState(plates)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!localPlates.trim()) {
      toast.error("Por favor ingresa las placas del vehículo")
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await actions.getAppointmentInfoForReschedule({
        plates: localPlates.trim().toUpperCase()
      })

      if (!result.data?.success) {
        toast.error(result.data?.error?.message || "Error al obtener información de la cita")
        setError(result.data?.error?.message || "Error al obtener información de la cita")
        return
      }

      setPlates(localPlates.trim().toUpperCase())
      setAppointmentInfo(result.data.data)
      setCurrentStep(2)

    } catch (error: any) {
      console.error("Error:", error)
      toast.error("Error al obtener información de la cita")
      setError("Error al obtener información de la cita")
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 p-3 bg-violet-100 rounded-full w-fit">
          <Calendar className="h-8 w-8 text-violet-700" />
        </div>
        <CardTitle className="text-2xl font-bold text-gray-900">
          Reagendar Cita
        </CardTitle>
        <p className="text-gray-600 mt-2">
          Ingresa las placas de tu vehículo para reagendar tu cita de mantenimiento
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="plates" className="text-sm font-medium text-gray-700">
              Placas del vehículo
            </Label>
            <Input
              id="plates"
              type="text"
              placeholder="Ej: ABC123"
              value={localPlates}
              onChange={(e) => setLocalPlates(e.target.value.toUpperCase())}
              className="w-full"
              disabled={loading}
              required
            />
          </div>

          <Button 
            type="submit" 
            className="w-full bg-violet-700 hover:bg-violet-800"
            disabled={loading || !localPlates.trim()}
          >
            {loading ? (
              <>
                <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                Buscando cita...
              </>
            ) : (
              "Buscar cita"
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
