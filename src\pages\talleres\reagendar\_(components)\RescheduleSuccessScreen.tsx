"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { CheckCircle, Calendar, Clock, MapPin, Home } from "lucide-react"
import { useReschedule } from "./RescheduleContext"
import { DateTime } from "luxon"

export default function RescheduleSuccessScreen() {
  const {
    appointmentInfo,
    selectedDate,
    selectedSlot
  } = useReschedule()

  if (!appointmentInfo?.appointment || !selectedDate || !selectedSlot) {
    return null
  }

  const { appointment } = appointmentInfo
  const { workshop, serviceType } = appointment

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTimeRange = (startTime: string) => {
    const start = DateTime.fromISO(startTime);
    const end = start.plus({ minutes: serviceType.duration });

    const startFormatted = start.toFormat('hh:mm a').toLowerCase();
    const endFormatted = end.toFormat('hh:mm a').toLowerCase();

    return `${startFormatted} - ${endFormatted}`;
  };

  const goToHome = () => {
    window.location.href = "/"
  }

  const scheduleAnother = () => {
    window.location.href = "/talleres/agendar"
  }

  return (
    <Card className="w-full">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 p-3 bg-green-100 rounded-full w-fit">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <CardTitle className="text-2xl font-bold text-gray-900">
          ¡Cita reagendada exitosamente!
        </CardTitle>
        <p className="text-gray-600 mt-2">
          Tu cita de mantenimiento ha sido reagendada correctamente
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Información de la nueva cita */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-medium text-green-900 mb-3">Detalles de tu nueva cita:</h4>
          
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <MapPin className="h-4 w-4 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-green-800">{workshop.name}</p>
                <p className="text-sm text-green-700">{serviceType.name}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-800 capitalize">
                {formatDate(selectedDate)}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-800">
                {formatTimeRange(selectedSlot)}
              </span>
            </div>
          </div>
        </div>

        {/* Información importante */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Información importante:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Llega 15 minutos antes de tu cita</li>
            <li>• Trae tu identificación oficial</li>
            <li>• Si necesitas cancelar, hazlo con al menos 24 horas de anticipación</li>
          </ul>
        </div>

        {/* Botones de acción */}
        <div className="space-y-3">
          <Button 
            onClick={goToHome}
            className="w-full bg-green-600 hover:bg-green-700"
          >
            <Home className="mr-2 h-4 w-4" />
            Ir al inicio
          </Button>
          
          <Button 
            variant="outline"
            onClick={scheduleAnother}
            className="w-full"
          >
            <Calendar className="mr-2 h-4 w-4" />
            Agendar otra cita
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
